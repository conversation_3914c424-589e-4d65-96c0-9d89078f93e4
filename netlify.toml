[build]
  base = "."
  command = "./scripts/netlify-build.sh"
  publish = "client/dist"

[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--prefix=/dev/null"

# Redirect all API calls to Netlify Functions
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/api/:splat"
  status = 200

# Redirect auth routes to Netlify Functions
[[redirects]]
  from = "/api/auth/*"
  to = "/.netlify/functions/api/auth/:splat"
  status = 200

# Redirect dashboard API calls to Netlify Functions
[[redirects]]
  from = "/dashboard/*"
  to = "/.netlify/functions/api/dashboard/:splat"
  status = 200

# SPA fallback for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Headers for CORS and security
[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization, Cookie"
    Access-Control-Allow-Credentials = "true"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# 🚀 Deployment Guide - Safety Induction System

## Netlify Deployment

### Prerequisites
1. GitHub repository with your code
2. Netlify account
3. PostgreSQL database (Neon recommended)

### Step 1: Prepare Environment Variables

#### Required Environment Variables for Netlify:
```bash
# Database
DATABASE_URL=your_postgresql_connection_string

# Better Auth
BETTER_AUTH_SECRET=your_secret_key_here
BETTER_AUTH_URL=https://your-app-name.netlify.app

# Netlify specific
URL=https://your-app-name.netlify.app
NETLIFY_URL=https://your-app-name.netlify.app

# Client
VITE_API_URL=https://your-app-name.netlify.app
```

### Step 2: Deploy to Netlify

1. **Connect Repository**
   - Go to [Netlify](https://netlify.com)
   - Click "New site from Git"
   - Connect your GitHub repository

2. **Configure Build Settings**
   - Build command: `./scripts/netlify-build.sh`
   - Publish directory: `client/dist`
   - Functions directory: `netlify/functions`

3. **Set Environment Variables**
   - Go to Site settings > Environment variables
   - Add all the environment variables listed above

4. **Deploy**
   - Click "Deploy site"
   - Wait for build to complete

### Step 3: Database Setup

1. **Create PostgreSQL Database**
   - Recommended: [Neon](https://neon.tech) (free tier available)
   - Get your connection string
   - Update `DATABASE_URL` in Netlify environment variables

2. **Run Migrations**
   - Migrations will run automatically during build
   - Check build logs to ensure they completed successfully

### Step 4: Create Admin User

After successful deployment:

1. **Option 1: Use the signup page**
   - Go to your deployed app
   - Sign up with email: `<EMAIL>`
   - Password: `SafetyAdmin2024!`

2. **Option 2: Manual database update**
   - Connect to your database
   - Find the user you created
   - Update the `role` field to `SUPERADMIN`

### Step 5: Test Functionality

1. **Test Authentication**
   - ✅ Sign up new user
   - ✅ Login with existing user
   - ✅ Logout functionality

2. **Test Admin Features**
   - ✅ Login as admin
   - ✅ Access admin dashboard
   - ✅ View user management
   - ✅ Check role-based access

3. **Test API Endpoints**
   - ✅ Dashboard stats
   - ✅ Visitor data
   - ✅ Protected routes

## Troubleshooting

### Common Issues

1. **Build Fails**
   - Check build logs in Netlify
   - Ensure all dependencies are installed
   - Verify environment variables are set

2. **Database Connection Issues**
   - Verify `DATABASE_URL` is correct
   - Check database is accessible from Netlify
   - Ensure SSL is enabled for PostgreSQL

3. **Authentication Not Working**
   - Check `BETTER_AUTH_SECRET` is set
   - Verify `BETTER_AUTH_URL` matches your domain
   - Check CORS settings in server

4. **Functions Not Working**
   - Check function logs in Netlify
   - Verify redirects are configured correctly
   - Ensure functions directory is correct

### Environment Variables Checklist

- [ ] `DATABASE_URL` - PostgreSQL connection string
- [ ] `BETTER_AUTH_SECRET` - Random secret key (32+ characters)
- [ ] `BETTER_AUTH_URL` - Your Netlify app URL
- [ ] `URL` - Your Netlify app URL
- [ ] `NETLIFY_URL` - Your Netlify app URL
- [ ] `VITE_API_URL` - Your Netlify app URL

### Default Admin Credentials

**Email:** `<EMAIL>`
**Password:** `SafetyAdmin2024!`

⚠️ **Important:** Change the admin password after first login!

## Production Checklist

- [ ] Environment variables configured
- [ ] Database migrations completed
- [ ] Admin user created
- [ ] Authentication working
- [ ] API endpoints responding
- [ ] CORS configured correctly
- [ ] SSL certificate active
- [ ] Custom domain configured (optional)

## Support

If you encounter issues:
1. Check Netlify build logs
2. Check function logs
3. Verify environment variables
4. Test database connectivity
5. Check browser console for errors

## Security Notes

1. **Change default admin password**
2. **Use strong `BETTER_AUTH_SECRET`**
3. **Enable email verification in production**
4. **Configure proper CORS origins**
5. **Use HTTPS only**

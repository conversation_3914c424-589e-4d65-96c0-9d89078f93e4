import { PrismaClient } from "@prisma/client";
import { auth } from "../lib/auth";

const prisma = new PrismaClient();

async function setupProduction() {
  try {
    console.log("🚀 Setting up production environment...");

    // Run database migrations
    console.log("📦 Running database migrations...");
    // Note: Migrations should be run via Netlify build process
    
    // Check if superadmin exists
    console.log("👤 Checking for existing superadmin...");
    const existingSuperAdmin = await prisma.user.findFirst({
      where: { role: "SUPERADMIN" }
    });

    if (!existingSuperAdmin) {
      console.log("🔧 Creating default superadmin...");
      
      // Create superadmin user
      const email = "<EMAIL>";
      const password = "SafetyAdmin2024!";
      const name = "Safety Induction Admin";

      try {
        // Create user using Better Auth
        const result = await auth.api.signUpEmail({
          body: {
            email,
            password,
            name,
          }
        });

        if (result?.user) {
          // Update user role to SUPERADMIN
          await prisma.user.update({
            where: { id: result.user.id },
            data: { role: "SUPERADMIN" }
          });

          console.log("✅ Superadmin created successfully:");
          console.log(`📧 Email: ${email}`);
          console.log(`🔑 Password: ${password}`);
          console.log("⚠️  Please change the password after first login!");
        }
      } catch (authError) {
        console.error("❌ Error creating superadmin:", authError);
      }
    } else {
      console.log("✅ Superadmin already exists");
    }

    console.log("🎉 Production setup completed!");

  } catch (error) {
    console.error("❌ Production setup failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run setup if this file is executed directly
if (import.meta.main) {
  setupProduction();
}

export { setupProduction };

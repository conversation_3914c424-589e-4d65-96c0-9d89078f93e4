import { Hono } from "hono";
import type { Variables } from "hono/types";
import { cors } from "hono/cors";
import type { ApiResponse, DashboardStats, ChartDataPoint, VisitorData } from "shared/dist";
import { auth } from "./lib/auth";
import { authMiddleware, requireRole } from "./middleware/auth";

// Define context variables type
type AppVariables = {
  user: any;
  session: any;
};

export const app = new Hono<{ Variables: AppVariables }>()

.use(cors({
  origin: (origin) => {
    const allowedOrigins = [
      "http://localhost:5173",
      "http://localhost:3000",
      "https://safety-induction.netlify.app",
      process.env.NETLIFY_URL || "https://safety-induction.netlify.app",
      process.env.URL || "https://safety-induction.netlify.app",
    ].filter(Boolean);

    // Allow Netlify app domains
    if (origin && origin.match(/https:\/\/.*\.netlify\.app$/)) {
      return origin;
    }

    // Allow specific origins
    if (allowedOrigins.includes(origin || "")) {
      return origin;
    }

    // Allow null origin (for mobile apps, Postman, etc.)
    if (!origin) {
      return "*";
    }

    return null;
  },
  credentials: true,
}))

// Better Auth routes
.all("/api/auth/*", async (c) => {
  return auth.handler(c.req.raw);
})

.get("/", (c) => {
	return c.text("Hello Hono!");
})

.get("/hello", async (c) => {
	const data: ApiResponse = {
		message: "Hello BHVR!",
		success: true,
	};

	return c.json(data, { status: 200 });
})

.get("/dashboard/stats", async (c) => {
	const stats: DashboardStats = {
		totalPengunjungTerdaftar: 17,
		totalPengunjungDisetujui: 10,
		totalPengunjungDitolak: 2,
		totalPengunjungKadaluwarsa: 5,
	};

	return c.json(stats, { status: 200 });
})

.get("/dashboard/chart", async (c) => {
	const chartData: ChartDataPoint[] = [
		{ day: "Senin", value: 40 },
		{ day: "Selasa", value: 35 },
		{ day: "Rabu", value: 45 },
		{ day: "Kamis", value: 95 },
		{ day: "Jumat", value: 80 },
		{ day: "Sabtu", value: 65 },
	];

	return c.json(chartData, { status: 200 });
})

.get("/dashboard/chart/weekly", async (c) => {
	const weeklyData: ChartDataPoint[] = [
		{ day: "Minggu 1", value: 280 },
		{ day: "Minggu 2", value: 320 },
		{ day: "Minggu 3", value: 290 },
		{ day: "Minggu 4", value: 410 },
	];

	return c.json(weeklyData, { status: 200 });
})

// Protected routes - require authentication
.use("/api/protected/*", authMiddleware)

// User routes (accessible by both USER and SUPERADMIN)
.get("/api/protected/profile", async (c) => {
	const user = c.get("user");
	return c.json({ user }, { status: 200 });
})

// Superadmin only routes
.use("/api/protected/admin/*", requireRole("SUPERADMIN"))

.get("/api/protected/admin/users", async (c) => {
	// This would fetch users from database in real implementation
	return c.json({ message: "Admin users endpoint", users: [] }, { status: 200 });
})

.get("/dashboard/visitors", async (c) => {
	const visitors: VisitorData[] = [
		{
			id: "1",
			name: "Rina Pratama",
			company: "PT. Teknologi Maju",
			type: "PAM",
			status: "approved",
			date: "2024-01-15",
			location: { lat: -6.2088, lng: 106.8456 }
		},
		{
			id: "2",
			name: "Budi Santoso",
			company: "CV. Berkah Jaya",
			type: "PAM",
			status: "pending",
			date: "2024-01-16",
			location: { lat: -6.1751, lng: 106.8650 }
		},
		{
			id: "3",
			name: "Sari Wijaya",
			company: "PT. Industri Besar",
			type: "PAM",
			status: "approved",
			date: "2024-01-17",
			location: { lat: -6.1751, lng: 106.8650 }
		},
		{
			id: "4",
			name: "Ahmad Fauzi",
			company: "PT. Maju Bersama",
			type: "PAM",
			status: "approved",
			date: "2024-01-18",
			location: { lat: -6.2088, lng: 106.8456 }
		},
		{
			id: "5",
			name: "Dewi Sartika",
			company: "CV. Sukses Mandiri",
			type: "SMA",
			status: "pending",
			date: "2024-01-19"
		},
		{
			id: "6",
			name: "Rudi Hermawan",
			company: "PT. Global Tech",
			type: "SMA",
			status: "approved",
			date: "2024-01-20",
			location: { lat: -6.1751, lng: 106.8650 }
		},
		{
			id: "7",
			name: "Maya Sari",
			company: "PT. Digital Solutions",
			type: "IBM",
			status: "rejected",
			date: "2024-01-21"
		},
		{
			id: "8",
			name: "Andi Wijaya",
			company: "CV. Inovasi Teknologi",
			type: "IBM",
			status: "approved",
			date: "2024-01-22",
			location: { lat: -6.2088, lng: 106.8456 }
		}
	];

	return c.json(visitors, { status: 200 });
});

const port = process.env.PORT || 3000;

export default {
  port,
  fetch: app.fetch,
};
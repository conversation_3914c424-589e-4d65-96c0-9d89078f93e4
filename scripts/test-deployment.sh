#!/bin/bash

echo "🧪 Testing deployment configuration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Check prerequisites
echo "📋 Checking prerequisites..."

command_exists bun
print_status $? "Bun is installed"

command_exists node
print_status $? "Node.js is installed"

command_exists npx
print_status $? "NPX is available"

# Check if required files exist
echo -e "\n📁 Checking required files..."

[ -f "netlify.toml" ]
print_status $? "netlify.toml exists"

[ -f "scripts/netlify-build.sh" ]
print_status $? "Build script exists"

[ -f "netlify/functions/api.ts" ]
print_status $? "Netlify function exists"

[ -f "server/.env" ]
print_status $? "Server environment file exists"

# Check environment variables
echo -e "\n🔧 Checking environment variables..."

if [ -f "server/.env" ]; then
    if grep -q "DATABASE_URL" server/.env; then
        echo -e "${GREEN}✅ DATABASE_URL is set${NC}"
    else
        echo -e "${RED}❌ DATABASE_URL is missing${NC}"
    fi
    
    if grep -q "BETTER_AUTH_SECRET" server/.env; then
        echo -e "${GREEN}✅ BETTER_AUTH_SECRET is set${NC}"
    else
        echo -e "${RED}❌ BETTER_AUTH_SECRET is missing${NC}"
    fi
else
    echo -e "${RED}❌ server/.env file not found${NC}"
fi

# Test build process
echo -e "\n🏗️ Testing build process..."

echo "Installing dependencies..."
bun install
print_status $? "Dependencies installed"

echo "Building shared package..."
cd shared && bun run build && cd ..
print_status $? "Shared package built"

echo "Generating Prisma client..."
cd server && npx prisma generate && cd ..
print_status $? "Prisma client generated"

echo "Building server..."
cd server && bun run build && cd ..
print_status $? "Server built"

echo "Building client..."
cd client && bun run build && cd ..
print_status $? "Client built"

# Check build outputs
echo -e "\n📦 Checking build outputs..."

[ -d "client/dist" ]
print_status $? "Client dist directory exists"

[ -f "client/dist/index.html" ]
print_status $? "Client index.html exists"

[ -d "server/dist" ]
print_status $? "Server dist directory exists"

[ -f "server/dist/index.js" ]
print_status $? "Server index.js exists"

# Test API endpoints (if server is running)
echo -e "\n🌐 Testing API configuration..."

# Check if server is running on port 3000
if curl -s http://localhost:3000 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Server is running on port 3000${NC}"
    
    # Test basic endpoint
    if curl -s http://localhost:3000/hello >/dev/null 2>&1; then
        echo -e "${GREEN}✅ API endpoint responding${NC}"
    else
        echo -e "${RED}❌ API endpoint not responding${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ Server not running (start with 'bun run dev:server')${NC}"
fi

echo -e "\n📋 Deployment Test Summary:"
echo "================================"
echo "1. Prerequisites: Check above for any missing tools"
echo "2. Files: Ensure all required files exist"
echo "3. Environment: Set all required environment variables"
echo "4. Build: All packages should build successfully"
echo "5. Outputs: Build artifacts should be generated"
echo ""
echo "🚀 If all checks pass, you're ready to deploy to Netlify!"
echo ""
echo "Next steps:"
echo "1. Push code to GitHub"
echo "2. Connect repository to Netlify"
echo "3. Set environment variables in Netlify"
echo "4. Deploy!"

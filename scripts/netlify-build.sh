#!/bin/bash

echo "🚀 Starting Netlify build process..."

# Install dependencies
echo "📦 Installing dependencies..."
bun install

# Build shared package first
echo "🔧 Building shared package..."
cd shared && bun run build && cd ..

# Generate Prisma client
echo "🗄️ Generating Prisma client..."
cd server && npx prisma generate && cd ..

# Run database migrations
echo "🔄 Running database migrations..."
cd server && npx prisma migrate deploy && cd ..

# Build server
echo "🏗️ Building server..."
cd server && bun run build && cd ..

# Build client
echo "🎨 Building client..."
cd client && bun run build && cd ..

# Install Netlify Functions dependencies
echo "⚡ Installing Netlify Functions dependencies..."
cd netlify/functions && npm install && cd ../..

echo "✅ Build completed successfully!"

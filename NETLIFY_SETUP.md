# 🌐 Netlify Setup Guide - Safety Induction System

## Quick Deploy Button

[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/yourusername/safety-induction)

## Manual Setup

### 1. Repository Setup
```bash
# Clone your repository
git clone https://github.com/yourusername/safety-induction.git
cd safety-induction

# Test deployment locally
./scripts/test-deployment.sh
```

### 2. Database Setup (Neon)
1. Go to [Neon](https://neon.tech)
2. Create a new project
3. Copy the connection string
4. Save it for environment variables

### 3. Netlify Configuration

#### Build Settings:
- **Build command:** `./scripts/netlify-build.sh`
- **Publish directory:** `client/dist`
- **Functions directory:** `netlify/functions`

#### Environment Variables:
```bash
DATABASE_URL=**********************************************
BETTER_AUTH_SECRET=your-32-character-secret-key
BETTER_AUTH_URL=https://your-app.netlify.app
URL=https://your-app.netlify.app
NETLIFY_URL=https://your-app.netlify.app
VITE_API_URL=https://your-app.netlify.app
```

### 4. Deploy Steps

1. **Connect to Netlify**
   - Go to [Netlify](https://app.netlify.com)
   - Click "New site from Git"
   - Choose your repository

2. **Configure Build**
   - Build command: `./scripts/netlify-build.sh`
   - Publish directory: `client/dist`

3. **Set Environment Variables**
   - Go to Site settings > Environment variables
   - Add all variables from above

4. **Deploy**
   - Click "Deploy site"
   - Wait for build to complete

### 5. Post-Deployment

1. **Access your app** at `https://your-app.netlify.app`

2. **Create admin user:**
   - Sign up with: `<EMAIL>`
   - Password: `SafetyAdmin2024!`
   - Or use the signup form with any email

3. **Set admin role** (if needed):
   ```sql
   UPDATE "user" SET role = 'SUPERADMIN' WHERE email = '<EMAIL>';
   ```

### 6. Testing Checklist

- [ ] Homepage loads
- [ ] Sign up works
- [ ] Login works
- [ ] Admin dashboard accessible
- [ ] API endpoints respond
- [ ] Database operations work

## Troubleshooting

### Build Fails
- Check build logs in Netlify dashboard
- Ensure all environment variables are set
- Verify database connection string

### Authentication Issues
- Check `BETTER_AUTH_SECRET` is set
- Verify `BETTER_AUTH_URL` matches your domain
- Clear browser cookies and try again

### Database Issues
- Verify `DATABASE_URL` is correct
- Check database is accessible
- Ensure migrations ran successfully

### Function Errors
- Check function logs in Netlify
- Verify API routes are working
- Check CORS configuration

## Environment Variables Generator

Use this to generate a secure `BETTER_AUTH_SECRET`:

```bash
# Generate a secure secret
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

## Custom Domain (Optional)

1. Go to Site settings > Domain management
2. Add custom domain
3. Configure DNS records
4. Update environment variables with new domain

## Security Recommendations

1. **Change default admin password**
2. **Use strong secrets**
3. **Enable email verification**
4. **Configure proper CORS**
5. **Use HTTPS only**

## Support

Need help? Check:
1. [Netlify Documentation](https://docs.netlify.com)
2. [Better Auth Documentation](https://better-auth.com)
3. [Prisma Documentation](https://prisma.io/docs)

## File Structure

```
.
├── netlify.toml              # Netlify configuration
├── netlify/functions/        # Serverless functions
├── scripts/                  # Build and deployment scripts
├── client/                   # React frontend
├── server/                   # Hono backend
├── shared/                   # Shared types
├── DEPLOYMENT.md            # Detailed deployment guide
└── NETLIFY_SETUP.md         # This file
```

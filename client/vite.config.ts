import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import path from 'path'

export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@client": path.resolve(__dirname, "./src"),
      "@server": path.resolve(__dirname, "../server/src"),
      "@shared": path.resolve(__dirname, "../shared/src"),
      "@": path.resolve(__dirname, "./src")
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom'],
          'router-vendor': ['react-router-dom'],
          'ui-vendor': ['@radix-ui/react-avatar', '@radix-ui/react-dropdown-menu', '@radix-ui/react-select', '@radix-ui/react-separator', '@radix-ui/react-slot', '@radix-ui/react-tabs'],
          'chart-vendor': ['recharts'],
          'icons-vendor': ['lucide-react'],
          'auth-vendor': ['better-auth'],
          // App chunks
          'superadmin': [
            './src/pages/superadmin/Dashboard.tsx',
            './src/pages/superadmin/ManajemenPengunjung.tsx',
            './src/pages/superadmin/ManajemenPengguna.tsx',
            './src/pages/superadmin/ManajemenKonten.tsx',
            './src/pages/superadmin/RoleManagement.tsx',
            './src/pages/superadmin/BackupRestore.tsx',
            './src/pages/superadmin/Logs.tsx'
          ]
        }
      }
    },
    chunkSizeWarningLimit: 600
  }
})

import { createAuthClient } from "better-auth/react";
import { adminClient } from "better-auth/client/plugins";

export const authClient = createAuthClient({
  baseURL: import.meta.env.VITE_API_URL || "https://safety-induction.netlify.app", // Use production URL
  fetchOptions: {
    credentials: "include",
  },
  plugins: [
    adminClient(),
  ],
});

export const { 
  signIn, 
  signUp, 
  signOut, 
  useSession,
  getSession 
} = authClient;

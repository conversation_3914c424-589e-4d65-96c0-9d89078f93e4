import { useState } from "react";
import { Card, CardContent } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "../../components/ui/tabs";
import { Input } from "../../components/ui/input";
import { Download, FileText, Printer } from "lucide-react";

interface LogEntry {
  id: string;
  no: number;
  name: string;
  email: string;
  role: string;
  ipAddress: string;
  loginDateTime: string;
}

export function Logs() {
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  const [logs] = useState<LogEntry[]>([
    {
      id: "1",
      no: 1,
      name: "admin",
      email: "<EMAIL>",
      role: "Admin",
      ipAddress: "36.555.845",
      loginDateTime: "2025-02-21 03:24:16",
    },
    {
      id: "2",
      no: 2,
      name: "admin",
      email: "<EMAIL>",
      role: "Admin",
      ipAddress: "44.185.169",
      loginDateTime: "2024-12-16 06:24:16",
    },
    {
      id: "3",
      no: 3,
      name: "admin",
      email: "<EMAIL>",
      role: "Admin",
      ipAddress: "55.99.758",
      loginDateTime: "2025-04-21 03:24:16",
    },
    {
      id: "4",
      no: 4,
      name: "user1",
      email: "<EMAIL>",
      role: "User",
      ipAddress: "*************",
      loginDateTime: "2025-01-15 09:30:45",
    },
    {
      id: "5",
      no: 5,
      name: "manager1",
      email: "<EMAIL>",
      role: "Manager",
      ipAddress: "*********",
      loginDateTime: "2025-01-14 14:22:33",
    }
  ]);

  const filteredLogs = logs.filter(log =>
    log.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.ipAddress.includes(searchTerm)
  );

  const totalPages = Math.ceil(filteredLogs.length / entriesPerPage);
  const startIndex = (currentPage - 1) * entriesPerPage;
  const paginatedLogs = filteredLogs.slice(startIndex, startIndex + entriesPerPage);

  const LogsContent = () => (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex flex-col sm:flex-row gap-3">
          <select
            value={entriesPerPage}
            onChange={(e) => {
              setEntriesPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
          </select>

          <Input
            type="text"
            placeholder="Search logs..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1);
            }}
            className="max-w-md"
          />
        </div>

        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <FileText className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
          <Button variant="outline" size="sm">
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
        </div>
      </div>

      {/* Logs Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="text-left p-4 font-medium text-gray-900">No</th>
                  <th className="text-left p-4 font-medium text-gray-900">Name</th>
                  <th className="text-left p-4 font-medium text-gray-900">Email</th>
                  <th className="text-left p-4 font-medium text-gray-900">Role</th>
                  <th className="text-left p-4 font-medium text-gray-900">IP Address</th>
                  <th className="text-left p-4 font-medium text-gray-900">Login Date Time</th>
                </tr>
              </thead>
              <tbody>
                {paginatedLogs.length > 0 ? (
                  paginatedLogs.map((log) => (
                    <tr key={log.id} className="border-b hover:bg-gray-50">
                      <td className="p-4 text-gray-900">{log.no}</td>
                      <td className="p-4 font-medium text-gray-900">{log.name}</td>
                      <td className="p-4 text-gray-600">{log.email}</td>
                      <td className="p-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          log.role === 'Admin' ? 'bg-orange-100 text-orange-800' :
                          log.role === 'Manager' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {log.role}
                        </span>
                      </td>
                      <td className="p-4 text-gray-600 font-mono">{log.ipAddress}</td>
                      <td className="p-4 text-gray-600">{log.loginDateTime}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="p-8 text-center text-gray-500">
                      No logs found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            Showing {startIndex + 1} to {Math.min(startIndex + entriesPerPage, filteredLogs.length)} of {filteredLogs.length} entries
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentPage(page)}
                className={currentPage === page ? "bg-orange-500 hover:bg-orange-600" : ""}
              >
                {page}
              </Button>
            ))}
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">Logs</h1>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="PAM" className="w-full">
        <div className="flex-shrink-0">
          <TabsList className="grid w-full grid-cols-3 max-w-sm">
            <TabsTrigger
              value="PAM"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              PAM
            </TabsTrigger>
            <TabsTrigger
              value="SMA"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              SMA
            </TabsTrigger>
            <TabsTrigger
              value="IBM"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              IBM
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="PAM" className="mt-4 lg:mt-6">
          <LogsContent />
        </TabsContent>

        <TabsContent value="SMA" className="mt-4 lg:mt-6">
          <LogsContent />
        </TabsContent>

        <TabsContent value="IBM" className="mt-4 lg:mt-6">
          <LogsContent />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default Logs;

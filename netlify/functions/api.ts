import { Handler } from '@netlify/functions';
import { app } from '../../server/src/index';

export const handler: Handler = async (event, context) => {
  const origin = event.headers.origin || event.headers.Origin;
  const allowedOrigins = [
    'https://safety-induction.netlify.app',
    'http://localhost:5173',
    'http://localhost:3000'
  ];

  const corsOrigin = allowedOrigins.includes(origin) ? origin : 'https://safety-induction.netlify.app';

  // Handle preflight OPTIONS requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': corsOrigin,
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, Cookie, X-Requested-With',
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Max-Age': '86400',
      },
      body: '',
    };
  }

  try {
    // Create a Request object from the Netlify event
    const url = new URL(event.path, `https://${event.headers.host}`);
    
    // Add query parameters
    if (event.queryStringParameters) {
      Object.entries(event.queryStringParameters).forEach(([key, value]) => {
        if (value) url.searchParams.append(key, value);
      });
    }

    const request = new Request(url.toString(), {
      method: event.httpMethod,
      headers: new Headers(event.headers as Record<string, string>),
      body: event.body || undefined,
    });

    // Process the request through the Hono app
    const response = await app.fetch(request);
    
    // Convert the Response to Netlify format
    const body = await response.text();
    const headers: Record<string, string> = {};
    
    response.headers.forEach((value, key) => {
      headers[key] = value;
    });

    // Ensure CORS headers are set
    headers['Access-Control-Allow-Origin'] = corsOrigin;
    headers['Access-Control-Allow-Credentials'] = 'true';
    headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
    headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Cookie, X-Requested-With';

    return {
      statusCode: response.status,
      headers,
      body,
    };
  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': corsOrigin,
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, Cookie, X-Requested-With',
      },
      body: JSON.stringify({ error: 'Internal server error' }),
    };
  }
};
